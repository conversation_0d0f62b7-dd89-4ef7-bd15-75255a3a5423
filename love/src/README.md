# Love项目新架构说明

## 📁 新目录结构

本文档说明Love项目架构优化后的新目录结构，采用前后端分离的设计原则。

### 🏗️ 架构概览

```
src/                                # 源代码根目录
├── 📁 server/                      # 后端代码目录
│   ├── 📁 routes/                  # 路由模块
│   │   ├── messages.js             # 留言API路由
│   │   ├── pages.js                # 页面路由
│   │   └── health.js               # 健康检查路由
│   ├── 📁 models/                  # 数据模型
│   │   └── Message.js              # 留言数据模型
│   ├── 📁 middleware/              # 中间件
│   │   ├── cors.js                 # CORS配置
│   │   ├── logger.js               # 日志中间件
│   │   └── error.js                # 错误处理
│   ├── 📁 utils/                   # 工具函数
│   │   ├── database.js             # 数据库工具
│   │   └── datetime.js             # 时间处理工具
│   └── app.js                      # Express应用主文件
└── 📁 client/                      # 前端代码目录
    ├── 📁 pages/                   # HTML页面
    │   ├── index.html              # 主页
    │   ├── together-days.html      # 在一起的日子
    │   ├── anniversary.html        # 纪念日页面
    │   ├── meetings.html           # 相遇记录
    │   └── memorial.html           # 纪念页面
    ├── 📁 styles/                  # 样式文件
    │   ├── main.css                # 主样式
    │   ├── pages.css               # 页面样式
    │   └── components.css          # 组件样式
    ├── 📁 scripts/                 # JavaScript文件
    │   ├── main.js                 # 主脚本
    │   ├── api.js                  # API调用
    │   ├── utils.js                # 工具函数
    │   └── romantic-quotes.js      # 浪漫话语数据
    └── 📁 assets/                  # 静态资源
        ├── 📁 fonts/               # 字体文件
        ├── 📁 images/              # 图片资源
        └── 📁 videos/              # 背景视频
```

## 🎯 设计原则

### 1. 前后端分离
- **server/**: 所有后端逻辑，包括API、数据库操作、中间件
- **client/**: 所有前端资源，包括HTML、CSS、JS、静态资源

### 2. 模块化设计
- **路由模块化**: 按功能分离路由处理
- **中间件抽离**: 独立的中间件模块
- **工具函数**: 可复用的工具函数

### 3. 资源统一管理
- **样式集中**: 统一管理CSS文件
- **脚本模块化**: JS文件按功能分离
- **静态资源**: 字体、图片、视频统一管理

## 🔄 迁移说明

### 从旧结构迁移
1. **后端文件**: `server.js` → `src/server/` 目录下的多个模块
2. **前端页面**: `html/` → `src/client/pages/`
3. **样式文件**: `style.css`, `pages.css` → `src/client/styles/`
4. **脚本文件**: `script.js`, `romantic-quotes.js` → `src/client/scripts/`
5. **静态资源**: `fonts/`, `background/` → `src/client/assets/`

### 保持不变的目录
- `data/`: 数据库和备份文件
- `logs/`: 日志文件
- `test/`: 测试文件
- `config/`: 配置文件

## 🚀 优势

1. **可维护性**: 代码结构清晰，职责分离明确
2. **可扩展性**: 模块化设计便于功能扩展
3. **开发效率**: 前后端分离，便于并行开发
4. **部署兼容**: 保持与宝塔面板部署的完全兼容

## 📝 注意事项

- 所有API接口保持不变，确保前端兼容性
- 静态资源路径需要相应调整
- 部署脚本需要适配新的目录结构
- 保持与现有manage.sh脚本的兼容性

---

**创建时间**: $(date '+%Y-%m-%d %H:%M:%S')
**架构版本**: v2.0
**兼容性**: 宝塔面板部署
