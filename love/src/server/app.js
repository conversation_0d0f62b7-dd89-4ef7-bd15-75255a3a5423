/**
 * Love项目后端主应用
 * 整合所有模块，提供完整的后端服务
 */

const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');

// 引入配置
const config = require('../../config/config');

// 引入中间件
const corsMiddleware = require('./middleware/cors');
const { requestLogger, logInfo, logError } = require('./middleware/logger');
const { errorHandler, notFoundHandler } = require('./middleware/error');

// 引入路由
const messagesRouter = require('./routes/messages');
const pagesRouter = require('./routes/pages');

// 引入数据库工具
const { initializeDatabase } = require('./utils/database');

// 创建Express应用
const app = express();
const PORT = config.server.port;

// 基础中间件
app.use(corsMiddleware);
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 请求日志中间件
app.use(requestLogger);

// 静态文件服务 - 只服务新架构的客户端文件
app.use('/src/client', express.static(path.join(__dirname, '../client')));

// API路由
app.use('/api/messages', messagesRouter);

// 其他API路由 - 临时直接在app.js中实现，后续可以拆分
const { db, getCurrentTimestamp, formatTimestamp, getBeijingDateString, getBeijingDateTimeString } = require('./utils/database');

// ===== 时光轴 API 接口 =====
app.get('/api/timeline', (req, res) => {
    db.all(
        'SELECT * FROM love_timeline WHERE status = "active" ORDER BY sort_order ASC, created_timestamp ASC',
        [],
        (err, rows) => {
            if (err) {
                console.error('Error fetching timeline:', err);
                return res.json({
                    success: false,
                    message: '获取时光轴数据失败: ' + err.message
                });
            }

            const timeline = rows.map(row => ({
                id: row.id,
                date: row.date,
                title: row.title,
                description: row.description,
                created_at: row.created_timestamp,
                timestamp: formatTimestamp(row.created_timestamp),
                beijing_date: row.beijing_date,
                beijing_datetime: row.beijing_datetime,
                sort_order: row.sort_order
            }));

            res.json({
                success: true,
                message: '',
                data: timeline
            });
        }
    );
});

// ===== 美好瞬间 API 接口 =====
app.get('/api/memories', (req, res) => {
    db.all(
        'SELECT * FROM love_memories WHERE status = "active" ORDER BY sort_order ASC, created_timestamp ASC',
        [],
        (err, rows) => {
            if (err) {
                console.error('Error fetching memories:', err);
                return res.json({
                    success: false,
                    message: '获取美好瞬间数据失败: ' + err.message
                });
            }

            const memories = rows.map(row => ({
                id: row.id,
                icon: row.icon,
                title: row.title,
                content: row.content,
                created_at: row.created_timestamp,
                timestamp: formatTimestamp(row.created_timestamp),
                beijing_date: row.beijing_date,
                beijing_datetime: row.beijing_datetime,
                sort_order: row.sort_order
            }));

            res.json({
                success: true,
                message: '',
                data: memories
            });
        }
    );
});

// ===== 现代情话 API 接口 =====
app.get('/api/modern-quotes', (req, res) => {
    const category = req.query.category || 'all';
    const limit = Math.min(parseInt(req.query.limit) || 50, 200);

    let query = 'SELECT * FROM modern_love_quotes WHERE status = "active"';
    let params = [];

    if (category !== 'all') {
        query += ' AND category = ?';
        params.push(category);
    }

    query += ' ORDER BY popularity_score DESC, created_timestamp DESC LIMIT ?';
    params.push(limit);

    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Error fetching modern quotes:', err);
            return res.json({
                success: false,
                message: '获取现代情话失败: ' + err.message
            });
        }

        const quotes = rows.map(row => ({
            id: row.id,
            content: row.content,
            source: row.source,
            category: row.category,
            language: row.language,
            created_at: row.created_timestamp,
            timestamp: formatTimestamp(row.created_timestamp),
            beijing_date: row.beijing_date,
            beijing_datetime: row.beijing_datetime,
            tags: row.tags ? JSON.parse(row.tags) : [],
            popularity_score: row.popularity_score
        }));

        res.json({
            success: true,
            data: quotes,
            category: category,
            total: quotes.length
        });
    });
});

// 页面路由
app.use('/', pagesRouter);

// API健康检查
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Love site backend is running',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '2.0.0'
    });
});

// 配置端点
app.get('/api/config', (req, res) => {
    res.json({
        success: true,
        data: {
            domain: {
                base: config.domain.base,
                url: config.domain.url
            },
            api: {
                prefix: config.api.prefix,
                baseUrl: `${config.domain.url}${config.api.prefix}`
            }
        }
    });
});

// 404处理
app.use(notFoundHandler);

// 错误处理中间件
app.use(errorHandler);

// 初始化数据库
initializeDatabase();

// 启动服务器
function startServer() {
    const server = app.listen(PORT, () => {
        logInfo(`Love site backend server is running on port ${PORT}`);
        logInfo(`Database path: ${config.database.path}`);
        console.log(`Love site backend server is running on port ${PORT}`);
        console.log(`Database path: ${config.database.path}`);
    });

    // 优雅关闭
    process.on('SIGINT', () => {
        logInfo('Shutting down gracefully...');
        console.log('\nShutting down gracefully...');
        
        server.close(() => {
            logInfo('HTTP server closed.');
            console.log('HTTP server closed.');
            
            // 关闭数据库连接
            const { db } = require('./utils/database');
            db.close((err) => {
                if (err) {
                    logError(err);
                    console.error('Error closing database:', err);
                } else {
                    logInfo('Database connection closed.');
                    console.log('Database connection closed.');
                }
                process.exit(0);
            });
        });
    });

    return server;
}

// 如果直接运行此文件，启动服务器
if (require.main === module) {
    startServer();
}

module.exports = { app, startServer };
