#!/bin/bash

# Anniversary.mp4 优化压缩脚本
# 针对高码率视频文件的特殊压缩策略
# 目标：570MB → 95MB (83%压缩率)

set -e

echo "🎬 Anniversary.mp4 优化压缩开始..."

# 创建输出目录
mkdir -p src/client/assets/videos-compressed
mkdir -p cloudinary-upload

# 检查原始文件
if [ ! -f "src/client/assets/videos/anniversary/anniversary.mp4" ]; then
    echo "❌ 原始文件不存在: src/client/assets/videos/anniversary/anniversary.mp4"
    exit 1
fi

echo "📊 原始文件信息："
ls -lh src/client/assets/videos/anniversary/anniversary.mp4

echo ""
echo "🔧 使用优化压缩参数："
echo "  - CRF: 25 (更激进的压缩)"
echo "  - 预设: veryslow (最佳压缩效率)"
echo "  - 分辨率: 2560x1440 (保持2K)"
echo "  - 音频: 128k (优化音频码率)"
echo "  - 两遍编码: 启用 (更好的码率控制)"

echo ""
echo "📹 开始第一遍分析..."

# 第一遍：分析
ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
    -c:v libx264 \
    -crf 25 \
    -preset veryslow \
    -profile:v high \
    -level 4.1 \
    -vf "scale=2560:1440:flags=lanczos" \
    -c:a aac \
    -b:a 128k \
    -movflags +faststart \
    -pix_fmt yuv420p \
    -pass 1 \
    -f null \
    /dev/null

echo ""
echo "📹 开始第二遍编码..."

# 第二遍：编码
ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
    -c:v libx264 \
    -crf 25 \
    -preset veryslow \
    -profile:v high \
    -level 4.1 \
    -vf "scale=2560:1440:flags=lanczos" \
    -c:a aac \
    -b:a 128k \
    -movflags +faststart \
    -pix_fmt yuv420p \
    -pass 2 \
    -y \
    src/client/assets/videos-compressed/anniversary_optimized.mp4

# 清理临时文件
rm -f ffmpeg2pass-0.log ffmpeg2pass-0.log.mbtree

echo ""
echo "📊 压缩结果对比："
echo "原始文件："
ls -lh src/client/assets/videos/anniversary/anniversary.mp4

echo "优化压缩："
ls -lh src/client/assets/videos-compressed/anniversary_optimized.mp4

# 计算压缩率
original_size=$(stat -c%s "src/client/assets/videos/anniversary/anniversary.mp4")
compressed_size=$(stat -c%s "src/client/assets/videos-compressed/anniversary_optimized.mp4")
compression_ratio=$(echo "scale=1; (1 - $compressed_size / $original_size) * 100" | bc)

echo ""
echo "📈 压缩统计："
echo "  原始大小: $(echo $original_size | numfmt --to=iec-i)B"
echo "  压缩大小: $(echo $compressed_size | numfmt --to=iec-i)B"
echo "  压缩率: ${compression_ratio}%"

# 检查目标大小
target_size=$((100 * 1024 * 1024))  # 100MB
if [ $compressed_size -le $target_size ]; then
    echo "✅ 压缩成功！文件大小符合Cloudinary要求 (<100MB)"
    
    # 替换原有的压缩文件
    echo ""
    echo "🔄 替换原有压缩文件..."
    cp src/client/assets/videos-compressed/anniversary_optimized.mp4 src/client/assets/videos-compressed/anniversary.mp4
    cp src/client/assets/videos-compressed/anniversary_optimized.mp4 cloudinary-upload/anniversary.mp4
    
    echo "✅ Anniversary.mp4 优化压缩完成！"
else
    echo "⚠️  文件仍然较大，但已显著压缩"
    echo "   如需进一步压缩，可考虑降低分辨率或使用CRF 28"
fi

echo ""
echo "📁 输出文件位置："
echo "  - 本地备份: src/client/assets/videos-compressed/anniversary.mp4"
echo "  - 上传准备: cloudinary-upload/anniversary.mp4"
echo "  - 优化版本: src/client/assets/videos-compressed/anniversary_optimized.mp4"
