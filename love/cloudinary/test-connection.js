#!/usr/bin/env node

/**
 * Cloudinary连接测试脚本
 * 验证所有6个账户的API连接状态
 */

const cloudinary = require('cloudinary').v2;
const config = require('../config/config.js');

// 测试单个账户连接
async function testAccountConnection(accountName, accountConfig) {
    try {
        // 配置Cloudinary
        cloudinary.config({
            cloud_name: accountConfig.cloudName,
            api_key: accountConfig.apiKey,
            api_secret: accountConfig.apiSecret
        });

        // 测试API连接 - 获取账户信息
        const result = await cloudinary.api.ping();
        
        console.log(`✅ ${accountName} (${accountConfig.cloudName}): 连接成功`);
        return { success: true, account: accountName, result };
    } catch (error) {
        console.log(`❌ ${accountName} (${accountConfig.cloudName}): 连接失败`);
        console.log(`   错误: ${error.message}`);
        return { success: false, account: accountName, error: error.message };
    }
}

// 测试所有账户
async function testAllConnections() {
    console.log('🔍 开始测试Cloudinary多账户连接...\n');
    
    const results = [];
    const accounts = config.cloudinary.accounts;
    
    for (const [accountName, accountConfig] of Object.entries(accounts)) {
        const result = await testAccountConnection(accountName, accountConfig);
        results.push(result);
        console.log(''); // 空行分隔
    }
    
    // 汇总结果
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    console.log('📊 测试结果汇总:');
    console.log(`   成功: ${successCount}/${totalCount} 个账户`);
    console.log(`   失败: ${totalCount - successCount}/${totalCount} 个账户`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 所有Cloudinary账户连接正常！');
        process.exit(0);
    } else {
        console.log('\n⚠️  部分账户连接失败，请检查API密钥配置');
        process.exit(1);
    }
}

// 主函数
async function main() {
    try {
        // 检查配置
        if (!config.cloudinary.enabled) {
            console.log('❌ Cloudinary未启用，请检查配置');
            process.exit(1);
        }
        
        await testAllConnections();
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    main();
}

module.exports = { testAccountConnection, testAllConnections };
