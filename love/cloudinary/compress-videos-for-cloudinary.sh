#!/bin/bash

# 高画质Cloudinary视频压缩脚本 (方案A)
# 使用CRF 18视觉无损压缩，2K分辨率输出
# 严格按照高画质Cloudinary部署指南方案A参数执行

echo "🎬 开始高画质视频压缩 (方案A - CRF 18视觉无损)..."

# 创建输出目录
mkdir -p src/client/assets/videos-compressed
mkdir -p cloudinary-upload

echo "📁 输出目录："
echo "  - 本地备份: src/client/assets/videos-compressed/"
echo "  - 上传准备: cloudinary-upload/"

# 压缩anniversary.mp4 (570MB -> ~95MB) - 方案A视觉无损
echo "📹 压缩anniversary.mp4 (方案A: CRF 18视觉无损)..."
ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
    -c:v libx264 \
    -crf 18 \
    -preset veryslow \
    -profile:v high \
    -level 4.1 \
    -vf "scale=2560:1440:flags=lanczos" \
    -c:a aac \
    -b:a 256k \
    -movflags +faststart \
    -pix_fmt yuv420p \
    -y \
    src/client/assets/videos-compressed/anniversary.mp4

# 复制到上传目录
cp src/client/assets/videos-compressed/anniversary.mp4 cloudinary-upload/

# 压缩together-days.mp4 (146MB -> ~75MB) - 方案A视觉无损
echo "📹 压缩together-days.mp4 (方案A: CRF 18视觉无损)..."
ffmpeg -i src/client/assets/videos/together-days/together-days.mp4 \
    -c:v libx264 \
    -crf 18 \
    -preset veryslow \
    -profile:v high \
    -level 4.1 \
    -vf "scale=2560:1440:flags=lanczos" \
    -c:a aac \
    -b:a 256k \
    -movflags +faststart \
    -pix_fmt yuv420p \
    -y \
    src/client/assets/videos-compressed/together-days.mp4

# 复制到上传目录
cp src/client/assets/videos-compressed/together-days.mp4 cloudinary-upload/

# 压缩memorial.mp4 (93MB -> ~65MB) - 方案A优化
echo "📹 压缩memorial.mp4 (方案A: CRF 18视觉无损)..."
ffmpeg -i src/client/assets/videos/memorial/memorial.mp4 \
    -c:v libx264 \
    -crf 18 \
    -preset veryslow \
    -profile:v high \
    -level 4.1 \
    -vf "scale=2560:1440:flags=lanczos" \
    -c:a aac \
    -b:a 256k \
    -movflags +faststart \
    -pix_fmt yuv420p \
    -y \
    src/client/assets/videos-compressed/memorial.mp4

# 复制到上传目录
cp src/client/assets/videos-compressed/memorial.mp4 cloudinary-upload/

# 压缩home.mp4 (63MB -> ~45MB) - 方案A优化
echo "📹 压缩home.mp4 (方案A: CRF 18视觉无损)..."
ffmpeg -i src/client/assets/videos/home/<USER>
    -c:v libx264 \
    -crf 18 \
    -preset veryslow \
    -profile:v high \
    -level 4.1 \
    -vf "scale=2560:1440:flags=lanczos" \
    -c:a aac \
    -b:a 256k \
    -movflags +faststart \
    -pix_fmt yuv420p \
    -y \
    src/client/assets/videos-compressed/home.mp4

# 复制到上传目录
cp src/client/assets/videos-compressed/home.mp4 cloudinary-upload/

# 压缩meetings.mp4 (39MB -> ~30MB) - 方案A优化
echo "📹 压缩meetings.mp4 (方案A: CRF 18视觉无损)..."
ffmpeg -i src/client/assets/videos/meetings/meetings.mp4 \
    -c:v libx264 \
    -crf 18 \
    -preset veryslow \
    -profile:v high \
    -level 4.1 \
    -vf "scale=2560:1440:flags=lanczos" \
    -c:a aac \
    -b:a 256k \
    -movflags +faststart \
    -pix_fmt yuv420p \
    -y \
    src/client/assets/videos-compressed/meetings.mp4

# 复制到上传目录
cp src/client/assets/videos-compressed/meetings.mp4 cloudinary-upload/

# 检查压缩结果
echo ""
echo "📊 方案A压缩结果对比："
echo "原始文件大小："
ls -lh src/client/assets/videos/*/*.mp4 | grep -E "(anniversary|together-days|memorial|home|meetings)" | sort

echo ""
echo "压缩后文件大小："
ls -lh src/client/assets/videos-compressed/*.mp4 2>/dev/null | sort

echo ""
echo "上传准备文件："
ls -lh cloudinary-upload/*.mp4 2>/dev/null | sort

echo ""
echo "✅ 方案A高画质压缩完成！"
echo "🎯 压缩参数: CRF 18 + veryslow + 2560x1440 + 256k音频"
echo "📁 本地备份: src/client/assets/videos-compressed/"
echo "📤 上传准备: cloudinary-upload/"
echo "🚀 所有文件已准备好上传到Cloudinary多账户系统"
