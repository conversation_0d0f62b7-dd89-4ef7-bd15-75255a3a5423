# 高画质 Cloudinary 多账户部署指南 (方案A) - AI助手实施版

## 📋 项目概述

本文档为AI助手提供Love网站高画质视频优化的完整实施方案。采用方案A (极致画质)，基于现有6个Cloudinary账户架构，实现视觉无损的桌面端视频体验。

### 🎯 核心目标

- ✅ **画质优先**: CRF 18视觉无损压缩，2K分辨率输出
- ✅ **多账户分散**: 6个专用账户，每页面独享25GB配额
- ✅ **桌面优化**: 专注2560x1440高端桌面体验
- ✅ **全局配置**: config.js统一管理，环境变量安全存储
- ✅ **清理机制**: 上传前自动清空所有远程库确保干净状态

### 🔧 技术规格 (方案A - 固定参数)

**压缩参数 (AI助手必须严格使用)**:
```bash
-c:v libx264               # H.264编码器
-crf 18                    # 视觉无损质量 (固定值)
-preset veryslow           # 最佳压缩效率 (固定值)
-profile:v high            # 高级配置文件
-level 4.1                 # 兼容性级别
-vf "scale=2560:1440:flags=lanczos"  # 2K分辨率+高质量缩放
-c:a aac                   # AAC音频编码
-b:a 256k                  # 高质量音频码率
-movflags +faststart       # 流媒体播放优化
-pix_fmt yuv420p           # 兼容性像素格式
-y                         # 覆盖输出文件
```

**预期效果**:
- 画质: 视觉无损 (SSIM > 0.98)
- 文件大小: 原始大小的30-50%
- 兼容性: 支持所有现代浏览器
- 加载速度: CDN加速 + 15秒超时回退

---

## 📊 多账户分配策略 (精确配置)

**重要**: 以下配置必须严格按照现有架构执行，不得修改账户映射关系。

| 页面标识 | 账户配置键 | YU编号 | 云名称 | API Key | 视频文件 | 月配额 | 优先级 |
|----------|------------|--------|--------|---------|----------|--------|--------|
| home | INDEX | YU0 | dcglebc2w | *************** | home.mp4 | 25GB | 1 |
| anniversary | ANNIVERSARY | YU1 | drhqbbqxz | *************** | anniversary.mp4 | 25GB | 1 |
| meetings | MEETINGS | YU2 | dkqnm9nwr | *************** | meetings.mp4 | 25GB | 1 |
| memorial | MEMORIAL | YU3 | ds14sv2gh | *************** | memorial.mp4 | 25GB | 1 |
| together-days | TOGETHER_DAYS | YU4 | dpq95x5nf | 934251748658618 | together-days.mp4 | 25GB | 1 |
| (备用) | BACKUP | YU5 | dtsgvqrna | 567337797774118 | 故障转移 | 25GB | 2 |

**配置要点**:
- 总配额: 150GB/月 (6 × 25GB)
- 文件夹: 所有账户统一使用 `love-website` 文件夹
- API密钥: 通过环境变量 `CLOUDINARY_SECRET_YU[0-5]` 安全存储
- 命名规则: YU0-YU5 对应 6个Cloudinary账户
- 映射逻辑: 页面名称 → 账户配置键 → YU编号 → 具体账户信息

---

## 📋 当前视频文件分析

**源文件路径**: `src/client/assets/videos/[页面名]/[页面名].mp4`

| 视频文件 | 当前大小 | 处理策略 | 预期压缩后大小 | Cloudinary限制 |
|----------|----------|----------|----------------|----------------|
| anniversary.mp4 | 570MB | 高压缩 | ~95MB | ✅ <100MB |
| together-days.mp4 | 146MB | 中压缩 | ~75MB | ✅ <100MB |
| memorial.mp4 | 93MB | 轻压缩 | ~65MB | ✅ <100MB |
| home.mp4 | 63MB | 优化 | ~45MB | ✅ <100MB |
| meetings.mp4 | 39MB | 优化 | ~30MB | ✅ <100MB |

**压缩策略说明**:
- 所有文件使用相同的方案A参数
- 确保压缩后文件小于100MB (Cloudinary限制)
- 保持2K分辨率和高画质
- 输出到两个位置: 本地备份 + 上传准备

---

## ⚙️ 全局配置系统 (AI助手实施指南)

### 🔧 config.js扩展 (必须精确执行)

**操作**: 在 `config/config.js` 文件中添加以下配置，插入到现有配置对象中。

```javascript
// 在 config/config.js 的 config 对象中添加以下部分
cloudinary: {
    // 全局开关
    enabled: true,
    
    // 方案A压缩参数 (固定配置，不可修改)
    compression: {
        preset: 'extreme_quality',  // 方案A标识
        crf: 18,                    // 视觉无损质量
        speed: 'veryslow',          // 最佳压缩效率
        resolution: '2560:1440',    // 2K分辨率
        audioBitrate: '256k',       // 高质量音频
        pixelFormat: 'yuv420p',     // 兼容性像素格式
        profile: 'high',            // H.264高级配置
        level: '4.1'                // 兼容性级别
    },
    
    // 多账户配置 (严格按照现有架构)
    accounts: {
        INDEX: {
            cloudName: 'dcglebc2w',
            apiKey: '***************',
            apiSecret: process.env.CLOUDINARY_SECRET_YU0,
            folder: 'love-website',
            quota: 25 * 1024 * 1024 * 1024, // 25GB
            priority: 1
        },
        ANNIVERSARY: {
            cloudName: 'drhqbbqxz',
            apiKey: '***************',
            apiSecret: process.env.CLOUDINARY_SECRET_YU1,
            folder: 'love-website',
            quota: 25 * 1024 * 1024 * 1024,
            priority: 1
        },
        MEETINGS: {
            cloudName: 'dkqnm9nwr',
            apiKey: '***************',
            apiSecret: process.env.CLOUDINARY_SECRET_YU2,
            folder: 'love-website',
            quota: 25 * 1024 * 1024 * 1024,
            priority: 1
        },
        MEMORIAL: {
            cloudName: 'ds14sv2gh',
            apiKey: '***************',
            apiSecret: process.env.CLOUDINARY_SECRET_YU3,
            folder: 'love-website',
            quota: 25 * 1024 * 1024 * 1024,
            priority: 1
        },
        TOGETHER_DAYS: {
            cloudName: 'dpq95x5nf',
            apiKey: '934251748658618',
            apiSecret: process.env.CLOUDINARY_SECRET_YU4,
            folder: 'love-website',
            quota: 25 * 1024 * 1024 * 1024,
            priority: 1
        },
        BACKUP: {
            cloudName: 'dtsgvqrna',
            apiKey: '567337797774118',
            apiSecret: process.env.CLOUDINARY_SECRET_YU5,
            folder: 'love-website',
            quota: 25 * 1024 * 1024 * 1024,
            priority: 2
        }
    },
    
    // 页面映射 (固定映射关系)
    pageMapping: {
        'home': 'INDEX',
        'anniversary': 'ANNIVERSARY',
        'meetings': 'MEETINGS',
        'memorial': 'MEMORIAL',
        'together-days': 'TOGETHER_DAYS'
    },
    
    // 加载器配置
    loader: {
        timeout: 15000,             // 15秒超时
        retries: 2,                 // 重试2次
        localFallback: true,        // 启用本地回退
        localPath: '/src/client/assets/videos-compressed',
        quality: 'q_auto:best,f_auto'  // Cloudinary高画质参数
    },
    
    // 清理配置
    cleanup: {
        enabled: true,              // 启用清理
        beforeUpload: true,         // 上传前清理
        mode: 'complete'            // 完全清理模式
    }
}
```

### 🔐 环境变量配置 (安全存储)

**操作**: 在项目根目录的 `.env` 文件中添加以下配置。

```bash
# Cloudinary API密钥 (YU0-YU5命名规则) - 真实密钥
# YU0: dcglebc2w (首页)
CLOUDINARY_SECRET_YU0="FfwmlQJX_0LOszwF6YF9KbnhmoU"

# YU1: drhqbbqxz (纪念日)
CLOUDINARY_SECRET_YU1="7g-JSBacW-ccz1cSAdkHw_wCrU8"

# YU2: dkqnm9nwr (相遇回忆)
CLOUDINARY_SECRET_YU2="juh_-_Amw-ds0gY03QL-E88oOIQ"

# YU3: ds14sv2gh (纪念相册)
CLOUDINARY_SECRET_YU3="ajE1x9E4Ynrg5AioDxJC_EZuTow"

# YU4: dpq95x5nf (在一起的日子)
CLOUDINARY_SECRET_YU4="849z0GBq5fapCwUCaZ0Ct0H4-5Y"

# YU5: dtsgvqrna (备用账户)
CLOUDINARY_SECRET_YU5="wgHrEwcNyzFyOceB9Q9yAHbteqc"

# 方案A配置
CLOUDINARY_ENABLED=true
NODE_ENV=production
```

**🔑 API密钥配置状态**:
✅ **所有账户密钥已配置完成** - 已提供6个账户的真实API密钥

**密钥验证信息**:
- YU0 (dcglebc2w): API Key *************** ✅
- YU1 (drhqbbqxz): API Key *************** ✅
- YU2 (dkqnm9nwr): API Key *************** ✅
- YU3 (ds14sv2gh): API Key *************** ✅
- YU4 (dpq95x5nf): API Key 934251748658618 ✅
- YU5 (dtsgvqrna): API Key 567337797774118 ✅

**完整账户信息汇总** (AI助手参考):

| YU编号 | 云名称 | API Key | API Secret | 页面 | 视频文件 |
|--------|--------|---------|------------|------|----------|
| YU0 | dcglebc2w | *************** | FfwmlQJX_0LOszwF6YF9KbnhmoU | home | home.mp4 |
| YU1 | drhqbbqxz | *************** | 7g-JSBacW-ccz1cSAdkHw_wCrU8 | anniversary | anniversary.mp4 |
| YU2 | dkqnm9nwr | *************** | juh_-_Amw-ds0gY03QL-E88oOIQ | meetings | meetings.mp4 |
| YU3 | ds14sv2gh | *************** | ajE1x9E4Ynrg5AioDxJC_EZuTow | memorial | memorial.mp4 |
| YU4 | dpq95x5nf | 934251748658618 | 849z0GBq5fapCwUCaZ0Ct0H4-5Y | together-days | together-days.mp4 |
| YU5 | dtsgvqrna | 567337797774118 | wgHrEwcNyzFyOceB9Q9yAHbteqc | (备用) | 故障转移 |

**安全注意事项**:
1. `.env` 文件不得提交到版本控制系统
2. 生产环境中使用服务器环境变量而非文件
3. API Secret必须从Cloudinary控制台获取
4. 每个账户的Secret都不相同，需要分别配置
5. 定期轮换API密钥以确保安全

### 🔍 前端配置暴露

**操作**: 在 `config/config.js` 文件末尾添加以下代码。

```javascript
// 在 config/config.js 文件末尾添加 (module.exports 之前)
if (typeof window !== 'undefined') {
    window.loveConfig = config;
}
```

---

## 🧹 清理机制设计 (确保库干净状态)

### 🎯 清理策略说明

**目标**: 确保每个Cloudinary账户的 `love-website` 文件夹完全干净，避免历史文件干扰。

**清理模式**: 采用完全清理模式
- 删除 `love-website` 文件夹下所有视频文件
- 确保库完全干净状态
- 避免文件名冲突和版本混乱

**清理时机**: 
1. 上传新文件前自动执行
2. 可手动执行清理操作
3. 支持确认机制防止误操作

### 🔧 清理实现逻辑 (AI助手参考)

**核心清理函数**:
```javascript
// 单账户清理函数
async function cleanupCloudinaryAccount(accountConfig) {
    const cloudinary = require('cloudinary').v2;
    
    // 配置当前账户
    cloudinary.config({
        cloud_name: accountConfig.cloudName,
        api_key: accountConfig.apiKey,
        api_secret: accountConfig.apiSecret
    });
    
    try {
        // 删除love-website文件夹下所有视频文件
        const result = await cloudinary.api.delete_resources_by_prefix(
            accountConfig.folder,
            { 
                resource_type: 'video',
                type: 'upload'
            }
        );
        
        console.log(`✅ 清理完成: ${accountConfig.cloudName} - 删除 ${result.deleted.length} 个文件`);
        return { success: true, deleted: result.deleted.length };
        
    } catch (error) {
        console.error(`❌ 清理失败: ${accountConfig.cloudName} - ${error.message}`);
        return { success: false, error: error.message };
    }
}
```

---

## 📁 文件结构规划 (AI助手创建指南)

**目标结构**:
```
love/
├── config/
│   ├── config.js                    # ✅ 已存在，需扩展Cloudinary配置
│   └── .env                         # 🔧 需创建，存储API密钥
├── cloudinary/
│   ├── 高画质Cloudinary部署指南.md   # ✅ 本文档
│   ├── compress-high-quality.sh     # 🔧 需创建，方案A压缩脚本
│   ├── upload-multi-account.js      # 🔧 需创建，多账户上传工具
│   ├── cleanup-accounts.js          # 🔧 需创建，账户清理工具
│   ├── high-quality-loader.js       # 🔧 需创建，前端加载器
│   └── test-loading.html            # 🔧 需创建，测试页面
├── src/client/assets/
│   ├── videos/                      # ✅ 已存在，原始视频文件
│   └── videos-compressed/           # 🔧 需创建，压缩后本地备份
└── cloudinary-upload/               # 🔧 需创建，上传准备目录
```

**创建优先级**:
1. 高优先级: compress-high-quality.sh, upload-multi-account.js
2. 中优先级: cleanup-accounts.js, high-quality-loader.js  
3. 低优先级: test-loading.html

---

## 🚀 部署流程设计 (AI助手执行步骤)

### 📋 第一步: 环境准备

**1.1 检查依赖**
```bash
# 检查ffmpeg是否安装 (必须)
ffmpeg -version

# 检查Node.js和npm
node --version
npm --version

# 安装Cloudinary SDK
npm install cloudinary dotenv
```

**1.2 配置环境变量**
- 创建 `.env` 文件 (如果不存在)
- 添加所有6个账户的API密钥
- 注意: 需要从Cloudinary控制台获取真实的API Secret

**1.3 扩展config.js**
- 在现有config.js中添加cloudinary配置
- 添加前端配置暴露代码
- 验证配置加载正确

### 📋 第二步: 视频压缩 (方案A)

**2.1 创建压缩脚本**
- 文件名: `cloudinary/compress-high-quality.sh`
- 使用方案A参数: CRF 18 + veryslow + 2560x1440
- 处理所有5个视频文件
- 输出到两个位置: `src/client/assets/videos-compressed/` 和 `cloudinary-upload/`

**2.2 执行压缩**
```bash
chmod +x cloudinary/compress-high-quality.sh
./cloudinary/compress-high-quality.sh
```

**2.3 验证压缩结果**
- 检查文件大小是否符合预期 (<100MB)
- 确认分辨率为2560x1440
- 验证视频质量和播放流畅性

### 📋 第三步: 清理远程库

**3.1 创建清理工具**
- 文件名: `cloudinary/cleanup-accounts.js`
- 实现批量清理所有6个账户
- 删除 `love-website` 文件夹下所有视频文件

**3.2 执行清理**
```bash
node cloudinary/cleanup-accounts.js --confirm
```

### 📋 第四步: 多账户上传

**4.1 创建上传工具**
- 文件名: `cloudinary/upload-multi-account.js`
- 按页面映射上传到对应账户
- 支持断点续传和错误重试

**4.2 执行上传**
```bash
node cloudinary/upload-multi-account.js
```

### 📋 第五步: 前端集成

**5.1 创建加载器**
- 文件名: `cloudinary/high-quality-loader.js`
- 实现多账户智能加载
- 支持15秒超时和本地回退

**5.2 创建测试页面**
- 文件名: `cloudinary/test-loading.html`
- 测试所有页面视频加载
- 验证加载性能和回退机制

---

## 🎯 关键实施要点 (AI助手必读)

### ⚠️ 严格要求

1. **压缩参数不可修改**: 必须使用方案A的固定参数
2. **账户映射不可变更**: 严格按照现有架构执行
3. **文件夹名称固定**: 所有账户使用 `love-website` 文件夹
4. **清理机制必须**: 上传前必须清理所有账户
5. **环境变量安全**: API密钥必须通过环境变量存储

### 🔍 验证检查点

1. **压缩质量**: 所有文件 <100MB，分辨率2560x1440
2. **账户清理**: 每个账户的love-website文件夹为空
3. **上传成功**: 每个视频文件上传到对应账户
4. **加载测试**: 前端能够正确加载所有视频
5. **回退机制**: Cloudinary失败时能回退到本地文件

### 📊 预期效果

- **画质**: 视觉无损 (SSIM > 0.98)
- **文件大小**: 减少50-70%
- **加载速度**: CDN加速，3-8秒加载完成
- **可用性**: 99.9% (多账户 + 本地回退)
- **配额**: 150GB/月总配额，每页面25GB

---

**文档版本**: v3.0.0 (AI助手优化版)
**创建时间**: 2025-01-31
**适用项目**: Love Website 高画质多账户优化
**维护者**: AI Assistant
