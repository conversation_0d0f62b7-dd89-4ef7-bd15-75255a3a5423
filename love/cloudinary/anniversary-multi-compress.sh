#!/bin/bash

# Anniversary.mp4 多版本压缩测试脚本
# 同时生成多个不同质量的压缩版本供选择
# 原始文件: 570MB, 22.5 Mbps, 2560x1440

set -e

echo "🎬 Anniversary.mp4 多版本压缩测试开始..."

# 创建输出目录
mkdir -p src/client/assets/videos-compressed/anniversary-test
mkdir -p cloudinary-upload

# 检查原始文件
if [ ! -f "src/client/assets/videos/anniversary/anniversary.mp4" ]; then
    echo "❌ 原始文件不存在: src/client/assets/videos/anniversary/anniversary.mp4"
    exit 1
fi

echo "📊 原始文件信息："
ls -lh src/client/assets/videos/anniversary/anniversary.mp4

echo ""
echo "🔧 将生成以下压缩版本："
echo "  1. CRF 18 (方案A原始参数) - 高质量保真"
echo "  2. CRF 21 (平衡版本) - 质量与大小平衡"
echo "  3. CRF 23 (中等压缩) - 适中压缩"
echo "  4. CRF 25 (激进压缩) - 更小文件"
echo "  5. CRF 28 (最激进) - 最小文件"
echo "  6. 两遍编码 CRF 23 - 最优质量"

# 定义压缩函数
compress_version() {
    local crf=$1
    local suffix=$2
    local description=$3
    local extra_params=$4
    
    echo ""
    echo "📹 开始压缩: $description (CRF $crf)"
    echo "   输出文件: anniversary_crf${crf}${suffix}.mp4"
    
    ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
        -c:v libx264 \
        -crf $crf \
        -preset veryslow \
        -profile:v high \
        -level 4.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -c:a aac \
        -b:a 192k \
        -movflags +faststart \
        -pix_fmt yuv420p \
        $extra_params \
        -y \
        "src/client/assets/videos-compressed/anniversary-test/anniversary_crf${crf}${suffix}.mp4" &
    
    echo "   进程ID: $!"
}

# 两遍编码函数
compress_two_pass() {
    local crf=$1
    local suffix="_2pass"
    
    echo ""
    echo "📹 开始两遍编码: CRF $crf"
    echo "   输出文件: anniversary_crf${crf}${suffix}.mp4"
    
    # 第一遍
    echo "   第一遍分析..."
    ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
        -c:v libx264 \
        -crf $crf \
        -preset veryslow \
        -profile:v high \
        -level 4.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -c:a aac \
        -b:a 192k \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -pass 1 \
        -f null \
        /dev/null
    
    # 第二遍
    echo "   第二遍编码..."
    ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
        -c:v libx264 \
        -crf $crf \
        -preset veryslow \
        -profile:v high \
        -level 4.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -c:a aac \
        -b:a 192k \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -pass 2 \
        -y \
        "src/client/assets/videos-compressed/anniversary-test/anniversary_crf${crf}${suffix}.mp4" &
    
    # 清理临时文件
    rm -f ffmpeg2pass-0.log ffmpeg2pass-0.log.mbtree
    
    echo "   进程ID: $!"
}

echo ""
echo "🚀 启动并行压缩任务..."

# 启动所有压缩任务
compress_version 18 "" "高质量保真版本" ""
sleep 2

compress_version 21 "" "质量平衡版本" ""
sleep 2

compress_version 23 "" "中等压缩版本" ""
sleep 2

compress_version 25 "" "激进压缩版本" ""
sleep 2

compress_version 28 "" "最激进压缩版本" ""
sleep 2

# 两遍编码版本
compress_two_pass 23

echo ""
echo "⏳ 所有压缩任务已启动，正在后台运行..."
echo "📊 可以使用以下命令查看进度："
echo "   ps aux | grep ffmpeg"
echo "   ls -lh src/client/assets/videos-compressed/anniversary-test/"

# 等待所有任务完成
echo ""
echo "⏳ 等待所有压缩任务完成..."

wait

echo ""
echo "✅ 所有压缩任务完成！"

# 显示结果对比
echo ""
echo "📊 压缩结果对比："
echo "原始文件:"
ls -lh src/client/assets/videos/anniversary/anniversary.mp4

echo ""
echo "压缩版本:"
ls -lh src/client/assets/videos-compressed/anniversary-test/

# 计算压缩率
echo ""
echo "📈 压缩率统计："
original_size=$(stat -c%s "src/client/assets/videos/anniversary/anniversary.mp4")

for file in src/client/assets/videos-compressed/anniversary-test/*.mp4; do
    if [ -f "$file" ]; then
        compressed_size=$(stat -c%s "$file")
        compression_ratio=$(echo "scale=1; (1 - $compressed_size / $original_size) * 100" | bc)
        filename=$(basename "$file")
        size_mb=$(echo "scale=1; $compressed_size / 1024 / 1024" | bc)
        
        echo "  $filename: ${size_mb}MB (压缩率: ${compression_ratio}%)"
    fi
done

echo ""
echo "🎯 推荐选择："
echo "  • 如需最高质量: anniversary_crf18.mp4"
echo "  • 如需平衡效果: anniversary_crf21.mp4 或 anniversary_crf23_2pass.mp4"
echo "  • 如需小文件: anniversary_crf25.mp4 或 anniversary_crf28.mp4"

echo ""
echo "📁 测试文件位置: src/client/assets/videos-compressed/anniversary-test/"
echo "💡 选择满意的版本后，可以复制到正式目录并重命名为 anniversary.mp4"
