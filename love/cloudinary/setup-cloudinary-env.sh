#!/bin/bash

# Cloudinary环境变量设置脚本
# 基于用户提供的API信息

echo "🔧 设置Cloudinary环境变量..."

# 设置环境变量
export CLOUDINARY_CLOUD_NAME="dcglebc2w"
export CLOUDINARY_API_KEY="899226968246286"
export CLOUDINARY_API_SECRET="FfwmlQJX_0LOszwF6YF9KbnhmoU"

echo "✅ 环境变量设置完成："
echo "   CLOUDINARY_CLOUD_NAME: $CLOUDINARY_CLOUD_NAME"
echo "   CLOUDINARY_API_KEY: $CLOUDINARY_API_KEY"
echo "   CLOUDINARY_API_SECRET: [已设置]"

# 验证环境变量
if [ -n "$CLOUDINARY_CLOUD_NAME" ] && [ -n "$CLOUDINARY_API_KEY" ] && [ -n "$CLOUDINARY_API_SECRET" ]; then
    echo "🎉 所有环境变量设置成功！"
    echo "💡 现在可以运行上传脚本了："
    echo "   node scripts/upload-to-cloudinary.js"
else
    echo "❌ 环境变量设置失败，请检查"
fi

# 创建.env文件（可选）
cat > .env << EOF
CLOUDINARY_CLOUD_NAME=dcglebc2w
CLOUDINARY_API_KEY=899226968246286
CLOUDINARY_API_SECRET=FfwmlQJX_0LOszwF6YF9KbnhmoU
EOF

echo "📄 已创建.env文件，包含Cloudinary配置"
echo "⚠️  请确保.env文件不会被提交到版本控制系统"

# 添加到.gitignore
if [ -f ".gitignore" ]; then
    if ! grep -q ".env" .gitignore; then
        echo ".env" >> .gitignore
        echo "✅ 已将.env添加到.gitignore"
    fi
else
    echo ".env" > .gitignore
    echo "✅ 已创建.gitignore并添加.env"
fi
