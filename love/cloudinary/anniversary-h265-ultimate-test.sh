#!/bin/bash

# Anniversary.mp4 H.265极高质量压缩测试
# 测试最接近无损的压缩方案
# 原始文件: 570MB, 22.5 Mbps, 2560x1440

set -e

echo "🎬 Anniversary.mp4 H.265极高质量压缩测试开始..."

# 创建输出目录
mkdir -p src/client/assets/videos-compressed/anniversary-h265-test
mkdir -p cloudinary-upload

# 检查原始文件
if [ ! -f "src/client/assets/videos/anniversary/anniversary.mp4" ]; then
    echo "❌ 原始文件不存在: src/client/assets/videos/anniversary/anniversary.mp4"
    exit 1
fi

echo "📊 原始文件信息："
ls -lh src/client/assets/videos/anniversary/anniversary.mp4

echo ""
echo "🔧 将生成以下H.265极高质量版本："
echo "  1. CRF 12 (接近无损) - 最高质量"
echo "  2. CRF 14 (视觉无损) - 极高质量"
echo "  3. CRF 16 (推荐无损) - 高质量平衡"
echo "  4. CRF 18 (优秀质量) - 质量参考"
echo "  5. 两遍编码 12Mbps - 精确控制"
echo "  6. 两遍编码 10Mbps - 高质量控制"

# H.265压缩函数
compress_h265() {
    local crf=$1
    local suffix=$2
    local description=$3
    local preset=${4:-slower}
    
    echo ""
    echo "📹 开始H.265压缩: $description (CRF $crf)"
    echo "   输出文件: anniversary_h265_crf${crf}${suffix}.mp4"
    echo "   预设: $preset"
    
    ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
        -c:v libx265 \
        -crf $crf \
        -preset $preset \
        -profile:v main \
        -level 5.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -y \
        "src/client/assets/videos-compressed/anniversary-h265-test/anniversary_h265_crf${crf}${suffix}.mp4" &
    
    echo "   进程ID: $!"
}

# H.265两遍编码函数
compress_h265_2pass() {
    local bitrate=$1
    local suffix="_2pass_${bitrate}k"
    local description="两遍编码 ${bitrate}k"
    
    echo ""
    echo "📹 开始H.265两遍编码: $description"
    echo "   输出文件: anniversary_h265${suffix}.mp4"
    echo "   目标码率: ${bitrate}k"
    
    # 第一遍分析
    echo "   第一遍分析..."
    ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
        -c:v libx265 \
        -b:v ${bitrate}k \
        -preset slower \
        -profile:v main \
        -level 5.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -pass 1 \
        -f null \
        /dev/null
    
    # 第二遍编码
    echo "   第二遍编码..."
    ffmpeg -i src/client/assets/videos/anniversary/anniversary.mp4 \
        -c:v libx265 \
        -b:v ${bitrate}k \
        -preset slower \
        -profile:v main \
        -level 5.1 \
        -vf "scale=2560:1440:flags=lanczos" \
        -pass 2 \
        -an \
        -movflags +faststart \
        -pix_fmt yuv420p \
        -y \
        "src/client/assets/videos-compressed/anniversary-h265-test/anniversary_h265${suffix}.mp4" &
    
    # 清理临时文件
    rm -f ffmpeg2pass-0.log ffmpeg2pass-0.log.mbtree
    
    echo "   进程ID: $!"
}

echo ""
echo "🚀 启动H.265极高质量压缩任务..."

# 启动CRF压缩任务
compress_h265 12 "" "接近无损版本" "slower"
sleep 3

compress_h265 14 "" "视觉无损版本" "slower"
sleep 3

compress_h265 16 "" "推荐无损版本" "slower"
sleep 3

compress_h265 18 "" "优秀质量版本" "slow"
sleep 3

# 启动两遍编码任务
compress_h265_2pass 12000
sleep 3

compress_h265_2pass 10000

echo ""
echo "⏳ 所有H.265压缩任务已启动，正在后台运行..."
echo "📊 可以使用以下命令查看进度："
echo "   ps aux | grep ffmpeg"
echo "   ls -lh src/client/assets/videos-compressed/anniversary-h265-test/"

# 创建监控函数
monitor_progress() {
    echo ""
    echo "📊 实时监控压缩进度..."
    
    while true; do
        local active_processes=$(ps aux | grep ffmpeg | grep anniversary | grep -v grep | wc -l)
        
        if [ "$active_processes" -eq 0 ]; then
            echo "✅ 所有压缩任务完成！"
            break
        fi
        
        echo "🔄 活跃进程: $active_processes"
        
        if [ -d "src/client/assets/videos-compressed/anniversary-h265-test" ]; then
            echo "📁 当前文件大小:"
            ls -lh src/client/assets/videos-compressed/anniversary-h265-test/ | grep -v "^total"
        fi
        
        echo "⏱️  $(date '+%H:%M:%S') - 等待30秒..."
        sleep 30
        echo ""
    done
}

# 等待所有任务完成
echo ""
echo "⏳ 等待所有压缩任务完成..."

wait

echo ""
echo "✅ 所有H.265压缩任务完成！"

# 显示详细结果对比
echo ""
echo "📊 H.265极高质量压缩结果对比："
echo "原始文件:"
ls -lh src/client/assets/videos/anniversary/anniversary.mp4

echo ""
echo "H.265压缩版本:"
ls -lh src/client/assets/videos-compressed/anniversary-h265-test/

# 计算压缩率和质量评估
echo ""
echo "📈 详细压缩统计："
original_size=$(stat -c%s "src/client/assets/videos/anniversary/anniversary.mp4")

for file in src/client/assets/videos-compressed/anniversary-h265-test/*.mp4; do
    if [ -f "$file" ]; then
        compressed_size=$(stat -c%s "$file")
        compression_ratio=$(echo "scale=1; (1 - $compressed_size / $original_size) * 100" | bc)
        filename=$(basename "$file")
        size_mb=$(echo "scale=1; $compressed_size / 1024 / 1024" | bc)
        
        # 质量评级
        if [[ "$filename" == *"crf12"* ]]; then
            quality="🌟🌟🌟🌟🌟 (接近无损)"
        elif [[ "$filename" == *"crf14"* ]]; then
            quality="🌟🌟🌟🌟⭐ (视觉无损)"
        elif [[ "$filename" == *"crf16"* ]]; then
            quality="🌟🌟🌟🌟⭐ (推荐无损)"
        elif [[ "$filename" == *"2pass_12000k"* ]]; then
            quality="🌟🌟🌟🌟⭐ (两遍12M)"
        elif [[ "$filename" == *"2pass_10000k"* ]]; then
            quality="🌟🌟🌟⭐⭐ (两遍10M)"
        else
            quality="🌟🌟🌟⭐⭐ (优秀质量)"
        fi
        
        echo "  $filename:"
        echo "    大小: ${size_mb}MB | 压缩率: ${compression_ratio}% | 质量: $quality"
        echo ""
    fi
done

echo ""
echo "🎯 Cloudinary兼容性分析："
echo "  • 免费计划限制: 100MB"
echo "  • 推荐选择: 文件大小 <95MB 的版本"

echo ""
echo "🏆 最终推荐："
echo "  • 最高质量: CRF 12 (如果 <100MB)"
echo "  • 平衡选择: CRF 14 或 CRF 16"
echo "  • 精确控制: 两遍编码版本"

echo ""
echo "📁 测试文件位置: src/client/assets/videos-compressed/anniversary-h265-test/"
echo "💡 选择满意的版本后，可以应用相同参数到其他视频文件"
