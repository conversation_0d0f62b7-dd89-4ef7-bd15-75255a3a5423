{"name": "love-site-backend", "version": "1.0.0", "description": "Backend service for love site messages", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"body-parser": "^1.20.2", "cloudinary": "^2.5.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "sqlite3": "^5.1.6"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["love", "messages", "backend"], "author": "", "license": "MIT"}